import 'package:digital_cv_mobile/controllers/cv_ats_controller.dart';
import 'package:digital_cv_mobile/controllers/form_controller.dart';
import 'package:digital_cv_mobile/controllers/get_info_cv_controller.dart';
import 'package:digital_cv_mobile/controllers/location_controller.dart';
import 'package:digital_cv_mobile/controllers/minat_konsep_controller.dart';
import 'package:digital_cv_mobile/controllers/profile_controller.dart';
import 'package:digital_cv_mobile/controllers/progress_screen_controller.dart';
import 'package:get/get.dart';

class FormScreeningBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<MinatKonsepController>(() => MinatKonsepController(),
        fenix: true);
    Get.lazyPut<ProgressScreenController>(() => ProgressScreenController(),
        fenix: true);
    Get.lazyPut<LocationController>(() => LocationController(), fenix: true);
    Get.lazyPut<GetInfoCVController>(() => GetInfoCVController(), fenix: true);
    Get.lazyPut<ProfileController>(() => ProfileController(), fenix: true);
    Get.lazyPut<FormController>(() => FormController(), fenix: true);
    Get.lazyPut<CvAtsController>(() => CvAtsController(), fenix: true);
  }
}
