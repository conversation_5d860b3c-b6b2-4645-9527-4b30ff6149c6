import 'package:equatable/equatable.dart';

class RhModel extends Equatable {
  RhModel({
    this.no,
    this.id,
    this.nama,
    this.tempatLahir,
    this.tglLahir,
    this.jenis<PERSON><PERSON><PERSON>,
    this.statusPern<PERSON>han,
    this.ktp,
    this.noTelepon,
    this.email,
    this.sim,
    this.provinsi,
    this.kota,
    this.kecamatan,
    this.rt,
    this.rw,
    this.alamat,
    this.kodePos,
    this.pendidikanTerakhir,
    this.diploma,
    this.kursus,
    this.pengalamanKerja,
    this.lamaPengalamanKerja,
    this.lamaPosisiKerja,
    this.perjalananDinas,
    this.minatLokasiKerja,
    this.minatGaji,
    this.bahasaAsing,
    this.kelebihan,
    this.kekurangan,
    this.ilmuKomputerisasi,
    this.memimpinTim,
    this.kemampuanPresentasi,
    this.lingkupPekerjaan,
    this.organisasi,
    this.createdAt,
    this.updatedAt,
    this.alamatLengkap,
    this.lingkupPekerjaanNama,
    this.tglLahirFormatted,
    this.tempatLahirFormatted,
  });

  final int? no;
  final String? id;
  final String? nama;
  final String? tempatLahir;
  final DateTime? tglLahir;
  final String? jenisKelamin;
  final String? statusPernikahan;
  final String? ktp;
  final String? noTelepon;
  final String? email;
  final String? sim;
  final String? provinsi;
  final String? kota;
  final String? kecamatan;
  final String? rt;
  final String? rw;
  final String? alamat;
  final String? kodePos;
  final String? pendidikanTerakhir;
  final String? diploma;
  final String? kursus;
  final String? pengalamanKerja;
  final String? lamaPengalamanKerja;
  final String? lamaPosisiKerja;
  final String? perjalananDinas;
  final String? minatLokasiKerja;
  final String? minatGaji;
  final String? bahasaAsing;
  final String? kelebihan;
  final String? kekurangan;
  final String? ilmuKomputerisasi;
  final String? memimpinTim;
  final String? kemampuanPresentasi;
  final String? lingkupPekerjaan;
  final String? organisasi;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? alamatLengkap;
  final String? lingkupPekerjaanNama;
  final String? tglLahirFormatted;
  final String? tempatLahirFormatted;

  factory RhModel.fromJson(Map<String, dynamic> json) {
    return RhModel(
      no: json["no"] ?? 0,
      id: json["id"] ?? "",
      nama: json["nama"] ?? "",
      tempatLahir: json["tempat_lahir"] ?? "",
      tglLahir: DateTime.tryParse(json["tgl_lahir"] ?? ""),
      jenisKelamin: json["jenis_kelamin"] ?? "",
      statusPernikahan: json["status_pernikahan"] ?? "",
      ktp: json["ktp"] ?? "",
      noTelepon: json["no_telepon"] ?? "",
      email: json["email"] ?? "",
      sim: json["sim"] ?? "",
      provinsi: json["provinsi"] ?? "",
      kota: json["kota"] ?? "",
      kecamatan: json["kecamatan"] ?? "",
      rt: json["rt"] ?? "",
      rw: json["rw"] ?? "",
      alamat: json["alamat"] ?? "",
      kodePos: json["kode_pos"] ?? "",
      pendidikanTerakhir: json["pendidikan_terakhir"] ?? "",
      diploma: json["diploma"] ?? "",
      kursus: json["kursus"] ?? "",
      pengalamanKerja: json["pengalaman_kerja"] ?? "",
      lamaPengalamanKerja: json["lama_pengalaman_kerja"] ?? "",
      lamaPosisiKerja: json["lama_posisi_kerja"] ?? "",
      perjalananDinas: json["perjalanan_dinas"] ?? "",
      minatLokasiKerja: json["minat_lokasi_kerja"] ?? "",
      minatGaji: json["minat_gaji"] ?? "",
      bahasaAsing: json["bahasa_asing"] ?? "",
      kelebihan: json["kelebihan"] ?? "",
      kekurangan: json["kekurangan"] ?? "",
      ilmuKomputerisasi: json["ilmu_komputerisasi"] ?? "",
      memimpinTim: json["memimpin_tim"] ?? "",
      kemampuanPresentasi: json["kemampuan_presentasi"] ?? "",
      lingkupPekerjaan: json["lingkup_pekerjaan"] ?? "",
      organisasi: json["organisasi"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      alamatLengkap: json["alamat_lengkap"] ?? "",
      lingkupPekerjaanNama: json["lingkup_pekerjaan_nama"] ?? "",
      tglLahirFormatted: json["tgl_lahir_formatted"] ?? "",
      tempatLahirFormatted: json["tempat_lahir_formatted"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        no,
        id,
        nama,
        tempatLahir,
        tglLahir,
        jenisKelamin,
        statusPernikahan,
        ktp,
        noTelepon,
        email,
        sim,
        provinsi,
        kota,
        kecamatan,
        rt,
        rw,
        alamat,
        kodePos,
        pendidikanTerakhir,
        diploma,
        kursus,
        pengalamanKerja,
        lamaPengalamanKerja,
        lamaPosisiKerja,
        perjalananDinas,
        minatLokasiKerja,
        minatGaji,
        bahasaAsing,
        kelebihan,
        kekurangan,
        ilmuKomputerisasi,
        memimpinTim,
        kemampuanPresentasi,
        lingkupPekerjaan,
        organisasi,
        createdAt,
        updatedAt,
        alamatLengkap,
        lingkupPekerjaanNama,
        tglLahirFormatted,
        tempatLahirFormatted,
      ];
}
