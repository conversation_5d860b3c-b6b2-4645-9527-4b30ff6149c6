import 'package:digital_cv_mobile/bindings/auth_binding.dart';
import 'package:digital_cv_mobile/bindings/form_screen_binding.dart';
import 'package:digital_cv_mobile/bindings/home_binding.dart';
import 'package:digital_cv_mobile/bindings/notifikasi_binding.dart';
import 'package:digital_cv_mobile/bindings/profile_binding.dart';
import 'package:digital_cv_mobile/bindings/timeline_binding.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/main_screen.dart';
import 'package:digital_cv_mobile/views/akun_screen.dart';
import 'package:digital_cv_mobile/views/daftar_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/cv_ats_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/riwayat_hidup_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/info_pekerjaan_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/informasi_pribadi_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/minat_konsep_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/organisasi_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/pelatihan_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/pendidikan_screen.dart';
import 'package:digital_cv_mobile/views/form_screening/pengalaman_kerja_screen.dart';
import 'package:digital_cv_mobile/views/login_screen.dart';
import 'package:digital_cv_mobile/views/lowongan_detail_screen.dart';
import 'package:digital_cv_mobile/views/lowongan_perusahaan_screen.dart';
import 'package:digital_cv_mobile/views/lupa_password_screen.dart';
import 'package:digital_cv_mobile/views/notifikasi_screen.dart';
import 'package:digital_cv_mobile/views/pengaturan_screen.dart';
import 'package:digital_cv_mobile/views/perusahaan_detail_screen.dart';
import 'package:digital_cv_mobile/views/profile_detail_screen.dart';
import 'package:digital_cv_mobile/views/timeline_screen.dart';
import 'package:digital_cv_mobile/views/tnc_screen.dart';
import 'package:digital_cv_mobile/views/ubah_email_screen.dart';
import 'package:digital_cv_mobile/views/ubah_no_telp_screen.dart';
import 'package:digital_cv_mobile/views/ubah_password_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Routes {
  static const home = "/";
  static const homeExplore = "/";
  static const login = "/login";
  static const register = "/register";
  static const akun = "/akun";
  static const changePassword = "/akun/ubah-password";
  static const changeEmail = "/akun/ubah-email";
  static const changePhoneNumber = "/akun/ubah-no-telp";
  static const resetPassword = "/reset-password";
  static const lupaPassword = "/lupa-password";
  static const setting = "/pengaturan";
  static const profile = "/profile";
  static const lowonganDetail = "/lowongan-detail";
  static const perusahaan = "/perusahaan";
  static const lowonganPerusahaan = "/perusahaan/lowongan";
  static const timeline = "/timeline";
  static const tnc = "/tnc";
  static const notifikasi = "/notifikasi";

  //Page Form Screening
  static const riwayatHidup = "/riwayat-hidup";
  static const cvAts = "/cv-ats";
  static const infoPribadi = "/cv/info-pribadi";
  static const pendidikan = "/cv/pendidikan";
  static const pelatihan = "/cv/pelatihan";
  static const pengalamanKerja = "/cv/pengalaman-kerja";
  static const infoPekerjaan = "/cv/info-pekerjaan";
  static const pengalamanOrganisasi = "/cv/pengalaman-organisasi";
  static const minatKonsep = "/cv/minat-konsep-pribadi";
}

class AppRoutes {
  static final pages = [
    GetPage(
      name: Routes.home,
      page: () => const MainScreen(),
      bindings: [
        HomeBinding(),
        ProfileBinding(),
      ],
      middlewares: [
        AuthMiddleware(),
      ],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.login,
      page: () => const LoginScreen(),
      binding: AuthBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.register,
      page: () => const DaftarScreen(),
      binding: AuthBinding(),
      transition: Transition.downToUp,
    ),
    GetPage(
      name: Routes.changePassword,
      page: () => UbahPasswordScreen(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.changeEmail,
      page: () => const UbahEmailScreen(),
      binding: ProfileBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.changePhoneNumber,
      page: () => const UbahNoTelpScreen(),
      binding: ProfileBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.lupaPassword,
      page: () => const LupaPasswordScreen(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.setting,
      page: () => const PengaturanScreen(),
      binding: AuthBinding(),
      transition: Transition.cupertinoDialog,
    ),
    GetPage(
      name: Routes.profile,
      page: () => const ProfileDetailScreen(),
      bindings: [
        AuthBinding(),
        ProfileBinding(),
      ],
      transition: Transition.cupertinoDialog,
    ),
    GetPage(
      name: Routes.akun,
      page: () => const AkunScreen(),
      bindings: [
        AuthBinding(),
        ProfileBinding(),
      ],
      transition: Transition.rightToLeft,
    ),
    GetPage(
        name: Routes.lowonganDetail,
        page: () => const LowonganDetailScreen(),
        transition: Transition.fadeIn,
        bindings: [FormScreeningBinding(), HomeBinding()]),
    GetPage(
      name: Routes.perusahaan,
      page: () => const PerusahaanDetailScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.lowonganPerusahaan,
      page: () => const LowonganPerusahaanScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.timeline,
      page: () => const TimelineScreen(),
      transition: Transition.fadeIn,
      binding: TimelineBinding(),
    ),
    GetPage(
      name: Routes.infoPribadi,
      page: () => const InfoPribadiScreen(),
      transition: Transition.downToUp,
      bindings: [
        FormScreeningBinding(),
        ProfileBinding(),
      ],
    ),
    GetPage(
        name: Routes.riwayatHidup,
        page: () => const RiwayatHidupScreen(),
        transition: Transition.cupertinoDialog,
        binding: FormScreeningBinding()),
    GetPage(
        name: Routes.cvAts,
        page: () => const CvAtsScreen(),
        transition: Transition.cupertinoDialog,
        binding: FormScreeningBinding()),
    GetPage(
      name: Routes.pendidikan,
      page: () => const RiwayatPendidikanScreen(),
      transition: Transition.downToUp,
      binding: FormScreeningBinding(),
    ),
    GetPage(
      name: Routes.pelatihan,
      page: () => const PelatihanScreen(),
      transition: Transition.downToUp,
      binding: FormScreeningBinding(),
    ),
    GetPage(
      name: Routes.pengalamanKerja,
      page: () => const PengalamanKerjaScreen(),
      transition: Transition.downToUp,
      binding: FormScreeningBinding(),
    ),
    GetPage(
      name: Routes.infoPekerjaan,
      page: () => const InfoPekerjaanScreen(),
      transition: Transition.downToUp,
      binding: FormScreeningBinding(),
    ),
    GetPage(
      name: Routes.pengalamanOrganisasi,
      page: () => const PengalamanOrganisasiScreen(),
      transition: Transition.downToUp,
      binding: FormScreeningBinding(),
    ),
    GetPage(
      name: Routes.minatKonsep,
      page: () => const MinatKonsepScreen(),
      transition: Transition.downToUp,
      binding: FormScreeningBinding(),
    ),
    GetPage(
      name: Routes.tnc,
      page: () => const TnCScreen(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.notifikasi,
      page: () => const NotifikasiScreen(),
      binding: NotifikasiBinding(),
      transition: Transition.rightToLeft,
    ),
  ];
}

class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final prefs = Get.find<SharedPreferences>();
    final isLoggedIn = prefs.getBool("auth") ?? false;
    final isGoogle = prefs.getBool("isGoogle") ?? false;

    LogService.log
        .i("🔍 Cek login status: auth=$isLoggedIn, isGoogle=$isGoogle");

    if (isLoggedIn) {
      return null;
    } else {
      return const RouteSettings(name: Routes.login);
    }
  }
}
