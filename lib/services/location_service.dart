import 'package:digital_cv_mobile/helpers/http_helper.dart';
import 'package:digital_cv_mobile/helpers/log_service.dart';
import 'package:digital_cv_mobile/helpers/url.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/utils.dart';

class LocationService {
  late final HttpClient _httpApiClient;
  final secureStorage = FlutterSecureStorage();

  LocationService() {
    _httpApiClient = HttpClient(baseUrl: api_uri);
  }

  Future<HttpResponse> getLokasi(String search, int page, int pageSize) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.get(
        "/get_locations.php", // Endpoint untuk mendapatkan provinsi
        queryParameters: {
          "func": "getLokasi",
          "q": search,
          "page": page,
          "page_size": pageSize,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getProvinsi(String search, int page) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.get(
        "/get_locations.php", // Endpoint untuk mendapatkan provinsi
        queryParameters: {
          "func": "getProvinsi",
          "q": search == "-" ? "" : search,
          "page": page,
          "page_size": 50,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getKota(String search, int page, int idProvince) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.get(
        "/get_locations.php", // Endpoint untuk mendapatkan provinsi
        queryParameters: {
          "func": "getKota",
          "q": search == "-" ? "" : search,
          "page": page,
          "page_size": 50,
          "province_id": idProvince,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }

  Future<HttpResponse> getKecamatan(
      String search, int page, int idRegency) async {
    try {
      var token = await secureStorage.read(key: "access_token");
      HttpResponse response = await _httpApiClient.get(
        "/get_locations.php", // Endpoint untuk mendapatkan provinsi
        queryParameters: {
          "func": "getKecamatan",
          "q": search == "-" ? "" : search,
          "page": page,
          "page_size": 50,
          "regency_id": idRegency,
        },
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          "Authorization": "Bearer $token",
        },
      );
      return response;
    } catch (e) {
      LogService.log.e("HTTP Error: $e");
      if (e.toString().contains('No internet connection')) {
        throw "controller.no_internet_connection".tr;
      } else {
        throw "controller.server_error".tr;
      }
    }
  }
}
