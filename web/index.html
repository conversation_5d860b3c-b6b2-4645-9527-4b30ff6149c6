<!DOCTYPE html>
<html lang="id">
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Digital CV - Platform untuk membuat dan mengelola CV digital yang profesional dan modern">
  <meta name="keywords" content="digital cv, resume, curriculum vitae, cv online, cv builder, karir, peker<PERSON>an">
  <meta name="author" content="Digital CV Team">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="Digital CV - Platform CV Digital Profesional">
  <meta property="og:description" content="Buat dan kelola CV digital yang profesional dan modern dengan mudah">
  <meta property="og:image" content="icons/Icon-512.png">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="Digital CV - Platform CV Digital Profesional">
  <meta property="twitter:description" content="Buat dan kelola CV digital yang profesional dan modern dengan mudah">
  <meta property="twitter:image" content="icons/Icon-512.png">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="Digital CV">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="152x152" href="icons/Icon-152.png">
  <link rel="apple-touch-icon" sizes="180x180" href="icons/Icon-180.png">
  <link rel="apple-touch-icon" sizes="167x167" href="icons/Icon-167.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="16x16" href="favicon.png"/>
  <link rel="icon" type="image/png" sizes="32x32" href="icons/Icon-32.png"/>
  <link rel="icon" type="image/png" sizes="96x96" href="icons/Icon-96.png"/>

  <title>Digital CV - Platform CV Digital Profesional</title>
  <link rel="manifest" href="manifest.json">

  <!-- Preload critical resources -->
  <link rel="preload" href="flutter.js" as="script">

  <!-- Theme color -->
  <meta name="theme-color" content="#2196F3">

  <script>
    // The value below is injected by flutter build, do not touch.
    const serviceWorkerVersion = null;
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>

  <style>
    /* Loading screen styles */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.5s ease-out;
    }

    .loading-container.fade-out {
      opacity: 0;
      pointer-events: none;
    }

    .loading-logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
      animation: pulse 2s infinite;
    }

    .loading-text {
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 30px;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }

    /* Hide loading when Flutter is ready */
    .flutter-ready .loading-container {
      display: none;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading" class="loading-container">
    <div class="loading-logo">
      <svg viewBox="0 0 24 24" fill="white">
        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
      </svg>
    </div>
    <div class="loading-text">Digital CV</div>
    <div class="loading-spinner"></div>
  </div>

  <script>
    // Show loading screen initially
    document.body.style.margin = '0';
    document.body.style.padding = '0';

    window.addEventListener('load', function(ev) {
      // Download main.dart.js
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            // Hide loading screen when Flutter is ready
            const loadingElement = document.getElementById('loading');
            if (loadingElement) {
              loadingElement.classList.add('fade-out');
              setTimeout(() => {
                loadingElement.style.display = 'none';
                document.body.classList.add('flutter-ready');
              }, 500);
            }
            appRunner.runApp();
          });
        }
      });
    });

    // Handle errors gracefully
    window.addEventListener('error', function(e) {
      console.error('Application error:', e.error);
      const loadingElement = document.getElementById('loading');
      if (loadingElement) {
        loadingElement.innerHTML = `
          <div style="color: white; text-align: center; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
            <h3>Terjadi kesalahan</h3>
            <p>Silakan refresh halaman atau coba lagi nanti.</p>
            <button onclick="window.location.reload()" style="background: white; color: #667eea; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-weight: 500;">
              Refresh Halaman
            </button>
          </div>
        `;
      }
    });
  </script>
</body>
</html>
